# ClickHouse 一键管理工具

这是为 Hikyuu 股票分析系统设计的 ClickHouse 数据库一键管理工具。

## 📁 文件说明

- `start_clickhouse.sh` - ClickHouse 一键启动脚本
- `stop_clickhouse.sh` - ClickHouse 一键关闭脚本
- `README_ClickHouse_Tools.md` - 本说明文档

## 🚀 启动 ClickHouse

```bash
./start_clickhouse.sh
```

### 功能特性：
- ✅ 自动检查服务是否已运行
- ✅ 检查端口占用情况
- ✅ 创建数据目录
- ✅ 后台启动服务
- ✅ 保存进程ID到文件
- ✅ 等待服务就绪
- ✅ 验证连接可用性
- ✅ 显示服务信息

### 输出信息：
- 🌐 HTTP 接口地址
- 🔌 TCP 接口地址  
- 📋 日志文件位置
- 🆔 进程ID文件位置
- 📊 数据库列表

## 🛑 关闭 ClickHouse

```bash
./stop_clickhouse.sh
```

### 功能特性：
- ✅ 检查服务运行状态
- ✅ 显示当前进程信息
- ✅ 优雅关闭（SIGTERM）
- ✅ 等待进程结束
- ✅ 强制终止（如需要）
- ✅ 清理PID文件
- ✅ 验证端口释放

### 关闭流程：
1. 发送 SIGTERM 信号优雅关闭
2. 等待最多30秒让进程自然结束
3. 如果超时，强制终止进程（SIGKILL）
4. 清理相关文件和资源

## 📊 配置参数

两个脚本使用相同的配置参数：

```bash
DATA_DIR="/Users/<USER>/Hikyuu/stock-clickhouse"  # 数据目录
HTTP_PORT="8123"                               # HTTP端口
TCP_PORT="9000"                                # TCP端口
LOG_FILE="$DATA_DIR/clickhouse.log"            # 日志文件
PID_FILE="$DATA_DIR/clickhouse.pid"            # 进程ID文件
```

## 🔧 使用场景

### 开发调试
```bash
# 启动数据库
./start_clickhouse.sh

# 进行开发工作...

# 关闭数据库
./stop_clickhouse.sh
```

### 系统重启后
```bash
# 重启后自动启动
./start_clickhouse.sh
```

### 故障排查
```bash
# 查看日志
tail -f /Users/<USER>/Hikyuu/stock-clickhouse/clickhouse.log

# 重启服务
./stop_clickhouse.sh
./start_clickhouse.sh
```

## 🎨 输出说明

脚本使用彩色输出提高可读性：
- 🔵 蓝色：信息提示
- 🟡 黄色：警告信息
- 🟢 绿色：成功信息
- 🔴 红色：错误信息

## 📝 日志文件

ClickHouse 的运行日志保存在：
```
/Users/<USER>/Hikyuu/stock-clickhouse/clickhouse.log
```

可以通过以下命令查看：
```bash
# 查看最新日志
tail -f /Users/<USER>/Hikyuu/stock-clickhouse/clickhouse.log

# 查看错误日志
grep -i error /Users/<USER>/Hikyuu/stock-clickhouse/clickhouse.log
```

## 🔍 故障排查

### 启动失败
1. 检查端口是否被占用：`lsof -i :8123`
2. 查看错误日志：`tail -20 /Users/<USER>/Hikyuu/stock-clickhouse/clickhouse.log`
3. 检查磁盘空间：`df -h`
4. 检查权限：确保对数据目录有读写权限

### 关闭失败
1. 手动查找进程：`ps aux | grep clickhouse`
2. 强制终止：`kill -9 <PID>`
3. 清理PID文件：`rm -f /Users/<USER>/Hikyuu/stock-clickhouse/clickhouse.pid`

## 🛠️ 自定义配置

如需修改配置，编辑脚本顶部的配置参数：

```bash
# 修改数据目录
DATA_DIR="/your/custom/path"

# 修改端口
HTTP_PORT="8124"
TCP_PORT="9001"
```

## 📞 技术支持

如遇问题，请检查：
1. ClickHouse 是否正确安装
2. 网络端口是否可用
3. 磁盘空间是否充足
4. 系统权限是否正确

---

**作者**: Hikyuu Stock Analysis System  
**版本**: 1.0  
**更新**: 2025-08-24
