#!/bin/bash

# ClickHouse 一键关闭脚本
# 作者: Hikyuu Stock Analysis System
# 功能: 安全关闭 ClickHouse 数据库服务

set -e  # 遇到错误立即退出

# 配置参数
DATA_DIR="/Users/<USER>/Hikyuu/stock-clickhouse"
HTTP_PORT="8123"
TCP_PORT="9000"
LOG_FILE="$DATA_DIR/clickhouse.log"
PID_FILE="$DATA_DIR/clickhouse.pid"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 ClickHouse 是否在运行
check_running() {
    if pgrep -f "clickhouse server" > /dev/null; then
        return 0  # 正在运行
    else
        return 1  # 未运行
    fi
}

# 获取 ClickHouse 进程ID
get_clickhouse_pids() {
    pgrep -f "clickhouse server" 2>/dev/null || true
}

# 等待进程结束
wait_for_shutdown() {
    local pid=$1
    local max_attempts=30
    local attempt=1
    
    print_message $YELLOW "等待进程 $pid 结束..."
    
    while [ $attempt -le $max_attempts ]; do
        if ! kill -0 $pid 2>/dev/null; then
            return 0  # 进程已结束
        fi
        
        printf "."
        sleep 1
        attempt=$((attempt + 1))
    done
    
    echo
    return 1  # 超时
}

# 强制终止进程
force_kill() {
    local pid=$1
    print_message $YELLOW "强制终止进程 $pid..."
    kill -9 $pid 2>/dev/null || true
    sleep 2
}

# 清理资源
cleanup() {
    # 删除PID文件
    if [ -f "$PID_FILE" ]; then
        print_message $YELLOW "🧹 清理PID文件: $PID_FILE"
        rm -f "$PID_FILE"
    fi
    
    # 检查端口是否释放
    if lsof -i :$HTTP_PORT > /dev/null 2>&1; then
        print_message $YELLOW "⚠️  端口 $HTTP_PORT 仍被占用"
    fi
    
    if lsof -i :$TCP_PORT > /dev/null 2>&1; then
        print_message $YELLOW "⚠️  端口 $TCP_PORT 仍被占用"
    fi
}

# 主函数
main() {
    print_message $BLUE "🛑 ClickHouse 一键关闭工具"
    print_message $BLUE "================================"
    
    # 检查是否在运行
    if ! check_running; then
        print_message $GREEN "✅ ClickHouse 服务未运行"
        
        # 清理可能残留的PID文件
        if [ -f "$PID_FILE" ]; then
            print_message $YELLOW "🧹 清理残留的PID文件"
            rm -f "$PID_FILE"
        fi
        
        exit 0
    fi
    
    # 显示当前运行的进程
    print_message $YELLOW "📋 当前运行的 ClickHouse 进程:"
    ps aux | grep "clickhouse server" | grep -v grep
    
    # 获取所有 ClickHouse 进程ID
    PIDS=$(get_clickhouse_pids)
    
    if [ -z "$PIDS" ]; then
        print_message $GREEN "✅ 没有找到 ClickHouse 进程"
        cleanup
        exit 0
    fi
    
    # 尝试优雅关闭
    print_message $YELLOW "🔄 尝试优雅关闭 ClickHouse 服务..."
    
    for pid in $PIDS; do
        print_message $BLUE "📤 向进程 $pid 发送 SIGTERM 信号"
        kill -TERM $pid 2>/dev/null || true
    done
    
    # 等待进程结束
    all_stopped=true
    for pid in $PIDS; do
        if ! wait_for_shutdown $pid; then
            print_message $YELLOW "⚠️  进程 $pid 未在预期时间内结束"
            all_stopped=false
        else
            print_message $GREEN "✅ 进程 $pid 已正常结束"
        fi
    done
    
    # 如果有进程未正常结束，强制终止
    if [ "$all_stopped" = false ]; then
        print_message $YELLOW "🔨 强制终止剩余进程..."
        
        remaining_pids=$(get_clickhouse_pids)
        for pid in $remaining_pids; do
            force_kill $pid
            
            if kill -0 $pid 2>/dev/null; then
                print_message $RED "❌ 无法终止进程 $pid"
            else
                print_message $GREEN "✅ 进程 $pid 已被强制终止"
            fi
        done
    fi
    
    # 最终检查
    if check_running; then
        print_message $RED "❌ ClickHouse 服务关闭失败"
        print_message $RED "仍有进程在运行:"
        ps aux | grep "clickhouse server" | grep -v grep
        exit 1
    else
        print_message $GREEN "✅ ClickHouse 服务已成功关闭"
        
        # 显示关闭后的状态
        echo
        print_message $BLUE "📊 服务状态:"
        print_message $GREEN "🔌 HTTP 端口 $HTTP_PORT: 已释放"
        print_message $GREEN "🔌 TCP 端口 $TCP_PORT: 已释放"
        
        if [ -f "$LOG_FILE" ]; then
            print_message $BLUE "📋 日志文件: $LOG_FILE (保留)"
        fi
    fi
    
    # 清理资源
    cleanup
    
    print_message $GREEN "🎉 ClickHouse 关闭完成！"
}

# 执行主函数
main "$@"
