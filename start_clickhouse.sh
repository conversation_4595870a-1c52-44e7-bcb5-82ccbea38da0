#!/bin/bash

# ClickHouse 一键启动脚本
# 作者: Hikyuu Stock Analysis System
# 功能: 启动 ClickHouse 数据库服务

set -e  # 遇到错误立即退出

# 配置参数
DATA_DIR="/Users/<USER>/Hikyuu/stock-clickhouse"
HTTP_PORT="8123"
TCP_PORT="9000"
LOG_FILE="$DATA_DIR/clickhouse.log"
PID_FILE="$DATA_DIR/clickhouse.pid"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 ClickHouse 是否已经在运行
check_running() {
    if pgrep -f "clickhouse server" > /dev/null; then
        return 0  # 正在运行
    else
        return 1  # 未运行
    fi
}

# 检查端口是否被占用（排除CLOSED状态的连接）
check_port() {
    local port=$1
    # 检查是否有LISTEN状态的连接
    if lsof -i :$port | grep -v CLOSED | grep -q LISTEN; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 等待服务启动
wait_for_service() {
    local max_attempts=30
    local attempt=1

    print_message $YELLOW "等待 ClickHouse 服务启动..."

    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://127.0.0.1:$HTTP_PORT/" -d "SELECT 1" > /dev/null 2>&1; then
            return 0
        fi

        printf "."
        sleep 1
        attempt=$((attempt + 1))
    done

    echo
    return 1
}

# 主函数
main() {
    print_message $BLUE "🚀 ClickHouse 一键启动工具"
    print_message $BLUE "================================"

    # 检查是否已经在运行
    if check_running; then
        print_message $YELLOW "⚠️  ClickHouse 服务已经在运行中"

        # 显示进程信息
        echo "当前运行的 ClickHouse 进程:"
        ps aux | grep "clickhouse server" | grep -v grep

        # 检查连接
        if curl -s "http://127.0.0.1:$HTTP_PORT/" -d "SELECT 'OK'" > /dev/null 2>&1; then
            print_message $GREEN "✅ 服务运行正常，可以正常连接"
        else
            print_message $RED "❌ 服务进程存在但无法连接，可能需要重启"
        fi
        exit 0
    fi

    # 检查端口占用
    if check_port $HTTP_PORT; then
        print_message $RED "❌ 端口 $HTTP_PORT 已被占用，请检查其他服务"
        lsof -i :$HTTP_PORT
        exit 1
    fi

    if check_port $TCP_PORT; then
        print_message $RED "❌ 端口 $TCP_PORT 已被占用，请检查其他服务"
        lsof -i :$TCP_PORT
        exit 1
    fi

    # 创建数据目录
    print_message $YELLOW "📁 创建数据目录: $DATA_DIR"
    mkdir -p "$DATA_DIR"

    # 切换到数据目录
    cd "$DATA_DIR"

    # 启动 ClickHouse 服务
    print_message $YELLOW "🔄 启动 ClickHouse 服务..."
    nohup clickhouse server > "$LOG_FILE" 2>&1 &

    # 获取并保存进程ID
    PID=$!
    echo $PID > "$PID_FILE"
    print_message $BLUE "📝 进程ID: $PID (已保存到 $PID_FILE)"

    # 等待服务启动
    if wait_for_service; then
        echo
        print_message $GREEN "✅ ClickHouse 启动成功！"
        print_message $GREEN "🌐 HTTP 接口: http://127.0.0.1:$HTTP_PORT"
        print_message $GREEN "🔌 TCP 接口: 127.0.0.1:$TCP_PORT"
        print_message $GREEN "📋 日志文件: $LOG_FILE"
        print_message $GREEN "🆔 进程文件: $PID_FILE"

        # 显示数据库信息
        echo
        print_message $BLUE "📊 数据库信息:"
        clickhouse-client --query "SELECT name FROM system.databases WHERE name NOT IN ('system', 'INFORMATION_SCHEMA', 'information_schema')" 2>/dev/null || true

    else
        echo
        print_message $RED "❌ ClickHouse 启动失败"
        print_message $RED "请检查日志文件: $LOG_FILE"

        # 显示最后几行日志
        if [ -f "$LOG_FILE" ]; then
            echo
            print_message $YELLOW "最近的错误日志:"
            tail -10 "$LOG_FILE"
        fi

        # 清理PID文件
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
