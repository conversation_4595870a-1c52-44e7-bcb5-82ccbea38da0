#!/bin/bash

# ClickHouse 启动脚本
# 数据目录
DATA_DIR="/Users/<USER>/Hikyuu/stock-clickhouse"

# 创建数据目录（如果不存在）
mkdir -p "$DATA_DIR"

# 切换到数据目录
cd "$DATA_DIR"

# 启动 ClickHouse 服务器
echo "Starting ClickHouse server in $DATA_DIR..."
clickhouse server > clickhouse.log 2>&1 &

# 获取进程ID
PID=$!
echo "ClickHouse started with PID: $PID"

# 等待几秒钟让服务启动
sleep 5

# 检查服务是否启动成功
if curl -s "http://127.0.0.1:8123/" -d "SELECT 'ClickHouse is running'" > /dev/null 2>&1; then
    echo "ClickHouse is running successfully!"
else
    echo "Failed to start ClickHouse"
    exit 1
fi
