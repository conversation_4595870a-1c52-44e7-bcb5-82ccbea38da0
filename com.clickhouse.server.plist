<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.clickhouse.server</string>
    
    <key>ProgramArguments</key>
    <array>
        <string>/opt/homebrew/bin/clickhouse</string>
        <string>server</string>
        <string>--config-file</string>
        <string>/Users/<USER>/Hikyuu/stock-clickhouse/config.xml</string>
    </array>
    
    <key>WorkingDirectory</key>
    <string>/Users/<USER>/Hikyuu/stock-clickhouse</string>
    
    <key>RunAtLoad</key>
    <true/>
    
    <key>KeepAlive</key>
    <true/>
    
    <key>StandardOutPath</key>
    <string>/Users/<USER>/Hikyuu/stock-clickhouse/clickhouse.log</string>
    
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/Hikyuu/stock-clickhouse/clickhouse.error.log</string>
    
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin</string>
    </dict>
</dict>
</plist>
